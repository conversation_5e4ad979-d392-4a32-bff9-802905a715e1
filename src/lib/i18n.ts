import { createI18n } from "@inlang/paraglide-sveltekit"
import { match as int } from "$lib/params/int"
import * as runtime from "$lib/paraglide/runtime.js"

export const i18n = createI18n(runtime, {
    defaultLanguageTag: "pl",
    exclude: [/^\/api/, /^\/sitemap\.xml$/, /^\/robots\.txt$/],
    pathnames: {
        '/professionals': {
            pl: '/dla-profesjonalistow',
            en: '/professionals',
            uk: '/dlya-medychnogo-zakladu',
        },
        '/professionals/products/syndose': {
            pl: '/dla-profesjonalistow/produkty/syndose',
            en: '/professionals/products/syndose',
            uk: '/dlya-medychnogo-zakladu/produkty/syndose',
        },
        '/professionals/products/wow': {
            pl: '/dla-profesjonalistow/produkty/wow',
            en: '/professionals/products/wow',
            uk: '/dlya-medychnogo-zakladu/produkty/wow',
        },
        '/professionals/products/wdm': {
            pl: '/dla-profesjonalistow/produkty/wdm',
            en: '/professionals/products/wdm',
            uk: '/dlya-medychnogo-zakladu/produkty/wdm',
        },
        '/professionals/products': {
            pl: '/dla-profesjonalistow/produkty',
            en: '/professionals/products',
            uk: '/dlya-medychnogo-zakladu/produkty',
        },
        '/contact': {
            pl: '/kontakt',
            en: '/contact-us',
            uk: '/kontakt',
        },
        '/knowledge/[slug]': {
            pl: '/poradnik-zdrowia/[slug]',
            en: '/knowledge/[slug]',
            uk: '/posibnyk-zdorovya/[slug]',
        },
        '/knowledge': {
            pl: '/poradnik-zdrowia',
            en: '/knowledge',
            uk: '/posibnyk-zdorovya',
        },
        '/legal': {
            pl: '/regulamin',
            en: '/legal',
            uk: '/pravyla',
        },
        '/privacy-policy': {
            pl: '/polityka-prywatnosci',
            en: '/privacy-policy',
            uk: '/privacy-policy',
        },
        '/articles': {
            pl: '/artykuly',
            en: '/articles',
            uk: '/statti',
        },
	},
    matchers: { int },
    prefixDefaultLanguage: "never",
    textDirection: {
        pl: "ltr",
        en: "ltr",
        uk: "ltr"
    },
})
