<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	export let posts: any;
  	import { PUBLIC_DIRECTUS_URL } from '$env/static/public';
	import Placeholder from '$lib/assets/images/placeholder.png'
</script>

{#if posts && posts.length > 0}
	{#each posts as post, index (post.id)}
		<a class="card block rounded-2xl bg-white" href={'/knowledge/'+post.id}>
			{#if post.cover}
				<img 
					class="rounded-tl-2xl rounded-tr-2xl object-cover w-full aspect-video transition-all"
					src={`${PUBLIC_DIRECTUS_URL}/assets/${post.cover}?width=720&format=webp`}
					width=722
					height=338
					alt={post.title}
					loading="lazy"
				/>
			{:else}
			<img 
				class="rounded-tl-2xl rounded-tr-2xl object-cover w-full aspect-video transition-all"
				src={Placeholder} 
				alt="Brak obrazka"
				loading="lazy"
			/>
			{/if}
			<div class="p-8 flex flex-col">
				<h1 class="text-blue-300 underline font-medium mb-4">{post.title}</h1>
				<div class="text-blue-300 text-sm">{@html (post.excerpt)}</div>
			</div>
		</a>
	{/each}
{:else}
	<p>{m.articles_not_found()}</p>
{/if}