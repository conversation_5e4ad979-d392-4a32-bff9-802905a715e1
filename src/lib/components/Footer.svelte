<script lang="ts">
	import fb from '$lib/assets/images/icons/fb.svg'
	import li from '$lib/assets/images/icons/li.svg'
	import x from '$lib/assets/images/icons/x.svg'

	import logoZbadani from '$lib/assets/images/logo-zbadani.svg'
	import logoSynektik from '$lib/assets/images/logo-synektik.svg'

	import * as m from '$lib/paraglide/messages.js';
	import { languageTag } from '$paraglide/runtime';

	$: lang = languageTag();

	import { onMount } from 'svelte';

	let isHidden = false;

	import { PUBLIC_RESULTS_URL } from '$env/static/public'
	const PUBLIC_RESULTS = PUBLIC_RESULTS_URL;

	onMount(() => {
		const ue = document.getElementById('ue');
		window.addEventListener('scroll', handleScroll);

		const isHiddenBefore = localStorage.getItem('isHidden');
		if (isHiddenBefore === 'true' && ue) {
			isHidden = true;
			ue.style.opacity = '0';
			ue.classList.add('transition-opacity', 'duration-500');
			//setTimeout(() => {  ue.style.display = 'none' }, 500);
		} else if (ue) {
			ue.style.opacity = '1';
			//setTimeout(() => { ue.style.display = 'fixed' }, 500);
		}

		window.addEventListener('beforeunload', () => {
      		localStorage.removeItem('isHidden');
    	});	

		return () => {
			window.removeEventListener('scroll', handleScroll);
		};
	});

	function handleScroll() {
		const ue = document.getElementById('ue');
		if (window.scrollY > 0 && !isHidden && ue) {
			ue.style.opacity = '0';
			ue.classList.add('transition-opacity', 'duration-500');
			isHidden = true;
			// Save state to local storage
			localStorage.setItem('isHidden', 'true');
		}
	}

</script>

<footer>
	<div id="footer-container" class="container">
		<div>
			<p><a href="/">{m.forPatient()}</a></p>
			<ul>
				<li><a href="{PUBLIC_RESULTS}" target="_blank">{m.results()}</a></li>
				<li><a href="https://pacjent.zbadani.pl/register" target="_blank">{m.register()}</a></li>
				<li><a href="https://pacjent.zbadani.pl/login" target="_blank">{m.login()}</a></li>
				<li><a href="/knowledge">{m.patient_patientZone()}</a></li>
				<li><a href="/contact">{m.contact()}</a></li>
			</ul>
		</div>
		<div>
			<p><a href="/professionals">{m.forMf()}</a></p>
			<ul>
				<li><a href="/professionals/products/syndose">{m.mf_syndose_dose()}</a></li>
				<li><a href="/professionals/products/wow">{m.mf_wwo()}</a></li>
				<li><a href="/professionals/products/wdm">{m.mf_wdm()}</a></li>
				<li><a href="/contact">{m.mf_ai()}</a></li>
				<li><a href="https://synektik.com.pl/produkty/?ref=zbadanipl" target="_blank">{m.synektik_offer()}</a></li>
			</ul>
		</div>
		<div class="ml-auto w-full lg:text-right">
			<p class="opacity-75">{m.findUs()}</p>
			<ul class="sm-list ml-auto lg:float-right">
				<li><a href="https://www.facebook.com/zbadani" target="blank"><img width="22" height="22" src="{fb}" alt="Facebook" /></a></li>
				<li><a href="https://pl.linkedin.com/showcase/zbadani-pl/" target="blank"><img width="22" height="22" src="{li}" alt="LinkedIn" /></a></li>
				<li><a href="https://twitter.com/Synektik1" target="blank"><img width="22" height="22" src="{x}" alt="X (Twitter)" /></a></li>
			</ul>
		</div>
	</div>
	<div class="postFooter">
		<div class="lg:flex container mx-auto mt-12 pt-12 mb-0 pb-0 border-t border-t-[rgba(255,255,255,0.2)]">
			<div class="lg:flex flex-row items-center">
				<a href="/" class="pr-8 mr-8 lg:border-r border-r-[rgba(255,255,255,0.2)]"><img width="126" height="25" src="{logoZbadani}" alt="Zbadani.pl" /></a>
				{#if lang == 'en'} 
					<a href="https://synektik.com.pl/en" target="blank"><img width="108" height="32" src="{logoSynektik}" alt="Synektik.com.pl" /></a>
				{:else} 
					<a href="https://synektik.com.pl/" target="blank"><img width="108" height="32" src="{logoSynektik}" alt="Synektik.com.pl" /></a>
				{/if}
			</div>
			<div class="ml-auto text-sm">
				<a href="/legal" class="mr-8 lg:inline-block block lg:my-0 my-4">{m.regulations()}</a>
				<a href="/privacy-policy" class="mr-8 lg:inline-block block  lg:my-0 my-4">{m.cookies()}</a>
				<a href="https://synektik.com.pl/o-firmie/#dofinansowane" target="_blank"  class="mr-8 lg:inline-block block  lg:my-0 my-4">{m.grants()}</a>
			</div>
		</div>
	</div>
</footer>

<div id="ue" class="opacity-0 fixed bottom-[12px]">
	<div class="p-[12px]">
		<div class="bg-white rounded-xl shadow-lg p-4 flex">
			<div class=""><img src="https://synektik.com.pl/wp-content/uploads/2023/07/ResizedImageWzUyMSw2NF0-mazowsze.png" alt=""></div>
		</div>
	</div>
</div>

<style lang="postcss">
	footer {
		@apply mt-auto pt-8 lg:pt-24 pb-4 lg:pb-12 bg-blue-300 text-white text-sm leading-loose font-normal;
	}
	footer a {
		@apply font-normal;
	}
	footer a:hover {
		@apply underline opacity-100;
	}
	footer p a {
		@apply uppercase opacity-75;
	}
	footer #footer-container {
		@apply grid grid-cols-1 lg:grid-cols-3 grid-flow-row gap-8 container mx-auto;
	}
	footer .sm-list {
		@apply mt-4 flex flex-row gap-6;
	}
	footer .sm-list li a {
		@apply transition-all;
	}
	footer .sm-list li a:hover {
		@apply opacity-50;
	}
</style>
