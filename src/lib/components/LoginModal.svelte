<script lang="ts">
  // @ts-nocheck
  import * as m from "$lib/paraglide/messages.js";

  // @ts-ignore
  import SolarLogin3Outline from "~icons/solar/login-3-outline";
  // @ts-ignore
  import SolarHealthOutline from "~icons/solar/health-outline";
  // @ts-ignore
  import SolarDocumentTextOutline from "~icons/solar/document-text-outline";
  // @ts-ignore
  import SolarLinkOutline from "~icons/solar/link-outline";
  // @ts-ignore
  import SolarCloseCircleOutline from "~icons/solar/close-circle-outline";

  export let show: boolean = false;

  const closeModal = () => {
    show = false;
  };

  const handleBackdropClick = (event: MouseEvent) => {
    if (event.target === event.currentTarget) {
      closeModal();
    }
  };

  const loginOptions = [
    {
      title: m.login_modal_diag_title(),
      description: m.login_modal_diag_desc(),
      url: "https://diagnostyka.zbadani.pl",
      icon: SolarLogin3Outline
    },
    {
      title: m.login_modal_syndose_title(),
      description: m.login_modal_syndose_desc(),
      url: "https://syndose.zbadani.pl",
      icon: SolarHealthOutline
    },
    {
      title: m.login_modal_wdm_title(),
      description: m.login_modal_wdm_desc(),
      url: "https://wdm.zbadani.pl",
      icon: SolarDocumentTextOutline
    },
    {
      title: m.login_modal_link_title(),
      description: m.login_modal_link_desc(),
      url: "https://link.zbadani.pl",
      icon: SolarLinkOutline
    }
  ];
</script>

{#if show}
  <div
    class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
    on:click={handleBackdropClick}
    on:keydown={(e) => e.key === 'Escape' && closeModal()}
    role="dialog"
    aria-modal="true"
    aria-labelledby="modal-title"
    tabindex="-1"
  >
    <div class="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
      <!-- Header -->
      <div class="flex items-center justify-between p-6">
        <h2 id="modal-title" class="text-2xl font-medium text-blue-300">
          {m.login_modal_title()}
        </h2>
        <button
          on:click={closeModal}
          class="w-12 h-12 bg-blue-900 rounded-full flex items-center justify-center text-blue-300 hover:text-blue-500 transition-colors"
          aria-label="Zamknij"
        >
          <SolarCloseCircleOutline class="text-3xl" />
        </button>
      </div>

      <!-- Content -->
      <div class="p-6 pt-0">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {#each loginOptions as option}
            <a
              href={option.url}
              target="_blank"
              class="flex align-items justify-center items-center p-4 bg-white border-2 rounded-xl border-blue-300 hover:border-blue-500 text-center text-blue-300 hover:text-blue-500"
              on:click={closeModal}
            >
              <div class="flex flex-col items-center space-y-4">
                <div>
                  <h3 class="font-base text-center">
                    {option.title}
                  </h3>
                </div>
              </div>
            </a>
          {/each}
        </div>
      </div>
    </div>
  </div>
{/if}

<style lang="postcss">
  /* Responsive adjustments for mobile */
  @media (max-width: 768px) {
    .grid {
      @apply grid-cols-1 gap-3;
    }

    .w-16.h-16 {
      @apply w-12 h-12;
    }
  }
</style>
