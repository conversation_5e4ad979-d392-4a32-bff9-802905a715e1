<script lang="ts">
	import Bg from '$lib/assets/images/hero/hero-wdm.jpg'

	import Banner from "$lib/components/Banner.svelte";

	import LogoWdm from '$lib/assets/images/logo_wdm_new.svg';

	import ImgScreenshot from "$lib/assets/images/wdm/image-screenshot.jpg";

    import Breadcrumbs from "$lib/components/Breadcrumbs.svelte";
    import WdmAccordion from "$lib/components/WdmAccordion.svelte";
    import Carousel from '$lib/components/Carousel.svelte';
    import { onMount, onDestroy } from 'svelte';
	import * as m from '$lib/paraglide/messages';
	import WhyChooseSection from '$lib/components/WhyChooseSection.svelte';
	import FinancingSection from '$lib/components/FinancingSection.svelte';
	import ProcessSection from '$lib/components/ProcessSection.svelte';
	import SeeAlsoSection from '$lib/components/SeeAlsoSection.svelte';
	import ContactSection from '$lib/components/ContactSection.svelte';

    // Sticky header functionality
    function initStickyHeader() {
        const stickyHeader = document.querySelector('.wdm-sticky-header') as HTMLElement;
        const comparisonSection = document.querySelector('.wdm-comparison') as HTMLElement;
        const featureItems = document.querySelectorAll('.wdm-feature-item');

        if (!stickyHeader || !comparisonSection) return;

        let isSticky = false;
        let ticking = false;
        let isMobile = window.innerWidth < 768;

        // Measure exact header dimensions to prevent jumps
        let headerHeight = stickyHeader.offsetHeight;
        let headerMarginBottom = parseInt(window.getComputedStyle(stickyHeader).marginBottom);
        let totalHeaderSpace = headerHeight + headerMarginBottom;

        // Function to check if mobile
        function checkMobile() {
            isMobile = window.innerWidth < 768;
        }

        // Add mobile labels
        function addMobileLabels() {
            if (!isMobile) return;

            const labels = ['Automatyczna', 'Manualne', 'Lista'];

            featureItems.forEach(item => {
                const values = item.querySelectorAll('.wdm-feature-value');
                values.forEach((value, index) => {
                    const htmlValue = value as HTMLElement;
                    if (index < labels.length) {
                        // Check if label already exists
                        const existingLabel = value.querySelector('.mobile-label');
                        if (!existingLabel) {
                            const label = document.createElement('div');
                            label.className = 'mobile-label';
                            label.style.cssText = 'font-weight: 600; color: #1e40af; margin-bottom: 4px; font-size: 0.875rem;';
                            label.textContent = labels[index];
                            value.insertBefore(label, value.firstChild);
                        }

                        // Make text left-aligned on mobile
                        htmlValue.style.textAlign = 'left';
                    }
                });
            });
        }

        // Remove mobile labels
        function removeMobileLabels() {
            const mobileLabels = document.querySelectorAll('.mobile-label');
            mobileLabels.forEach(label => label.remove());

            // Reset text alignment
            const values = document.querySelectorAll('.wdm-feature-value');
            values.forEach(value => {
                (value as HTMLElement).style.textAlign = '';
            });
        }

        // Add padding to prevent content jump - use exact measurements
        function addPaddingToSection() {
            if (!isSticky) return;
            comparisonSection.style.paddingTop = `${totalHeaderSpace}px`;
        }

        // Remove padding when not sticky
        function removePaddingFromSection() {
            comparisonSection.style.paddingTop = '0';
        }

        // Handle sticky behavior
        function handleSticky() {
            if (!stickyHeader || !comparisonSection) return;

            // Handle mobile view
            if (isMobile) {
                if (isSticky) {
                    isSticky = false;
                    stickyHeader.classList.remove('stuck');
                    stickyHeader.style.position = 'relative';
                    stickyHeader.style.top = 'auto';
                    removePaddingFromSection();
                }
                addMobileLabels();
                return;
            }

            // Desktop view - remove mobile labels
            removeMobileLabels();

            // Get section boundaries
            const sectionRect = comparisonSection.getBoundingClientRect();
            const sectionTop = sectionRect.top;
            const sectionBottom = sectionRect.bottom;

            // Check if we're scrolling within the section
            const shouldBeSticky = sectionTop <= 0 && sectionBottom > headerHeight;

            // Apply changes only when state actually changes to avoid glitches
            if (shouldBeSticky && !isSticky) {
                // First add padding to prevent jump
                comparisonSection.style.paddingTop = `${totalHeaderSpace}px`;

                // Then make header sticky
                isSticky = true;
                stickyHeader.classList.add('stuck');

                // Apply fixed styles
                stickyHeader.style.position = 'fixed';
                stickyHeader.style.top = '0';
                stickyHeader.style.left = '0';
                stickyHeader.style.right = '0';
                stickyHeader.style.width = '100%';
                stickyHeader.style.zIndex = '100';
                stickyHeader.style.backgroundColor = 'white';
                stickyHeader.style.borderRadius = '0';
                stickyHeader.style.borderBottom = '1px solid #BECDF8';
                stickyHeader.style.borderTop = 'none';
                stickyHeader.style.borderLeft = 'none';
                stickyHeader.style.borderRight = 'none';
                stickyHeader.style.padding = '1rem';
                stickyHeader.style.marginLeft = 'calc(-50vw + 50%)';
                stickyHeader.style.marginBottom = '0';
            } else if (!shouldBeSticky && isSticky) {
                isSticky = false;
                stickyHeader.classList.remove('stuck');

                // First reset styles
                stickyHeader.style.cssText = '';
                stickyHeader.className = 'wdm-sticky-header mb-6 hidden md:flex w-full gap-8 bg-white py-4 px-4 rounded-xl border border-[#BECDF8] ';

                // Then remove padding
                removePaddingFromSection();
            }
        }

        // Smooth scroll handler
        function onScroll() {
            requestAnimationFrame(() => {
                handleSticky();
            });
        }

        // Throttled resize handler
        function onResize() {
            if (!ticking) {
                requestAnimationFrame(() => {
                    // Recalculate header dimensions on resize
                    const newHeaderHeight = stickyHeader.offsetHeight;
                    const newHeaderMarginBottom = parseInt(window.getComputedStyle(stickyHeader).marginBottom);

                    if (newHeaderHeight !== headerHeight) {
                        headerHeight = newHeaderHeight;
                        headerMarginBottom = newHeaderMarginBottom;
                        totalHeaderSpace = headerHeight + headerMarginBottom;
                    }

                    checkMobile();
                    handleSticky();
                    ticking = false;
                });
                ticking = true;
            }
        }

        // Initialize
        checkMobile();
        handleSticky();

        // Add event listeners
        window.addEventListener('scroll', onScroll, { passive: true });
        window.addEventListener('resize', onResize, { passive: true });

        // Cleanup function
        return () => {
            window.removeEventListener('scroll', onScroll);
            window.removeEventListener('resize', onResize);
        };
    }

	// Initialize on mount
	let cleanup: (() => void) | undefined;

	onMount(() => {
		// Small delay to ensure DOM is fully rendered
		setTimeout(() => {
			cleanup = initStickyHeader();
		}, 100);
	});

	onDestroy(() => {
		if (cleanup) {
			cleanup();
		}
	});



</script>

<svelte:head>
	<title>{m.wdm_page_title()}</title>
	<meta name="description" content={m.wdm_page_description()} />
</svelte:head>

<Banner src={Bg}>
	<div slot="bannerContent" class="text-blue-300">
		<Breadcrumbs current={m.wdm_breadcrumb()} previous={m.forMf()} previousUrl="/dla-profesjonalistow" />
		<div class="text-center flex mb-4 items-center">
			<img src={LogoWdm} alt="Logo WDM" class="max-h-32">
		</div>
		<p class="mb-4 text-blue-100">{m.wdm_banner_desc()}</p>
		<a href="#skontaktuj-sie-z-nami" class="btn btn-fill mt-4">{m.wdm_banner_btn()}</a>
	</div>
</Banner>

<div class="bg-blue-900">
	<main>

		<section id="dla-kogo" class="bg-blue-900 !pb-0">
			<div class="container mx-auto md:flex items-center md:gap-16">
				<div class="md:w-1/2">
					<h3 class="mb-4">{m.wdm_for_whom_title()}</h3>
					<p>{m.wdm_for_whom_p()}</p>
					<ul class="list-disc list-outside pl-4">
						<li class="py-2">{m.wdm_for_whom_1()}</li>
						<li class="py-2">{m.wdm_for_whom_2()}</li>
					</ul>
					<p><br />{m.wdm_for_whom_p2()}</p>
				</div>
				<div class="md:w-1/2 md:px-8">
					<img src="{ImgScreenshot}" alt="Zrzut ekranu" class="rounded-lg shadow-[0_35px_60px_-15px_rgba(0,42,163,0.1)]">
				</div>
			</div>
		</section>

		<section id="kluczowe-funkcje" class="bg-blue-900">
			<h3 class="mb-12 text-center">{m.wdm_key_features_title()}</h3>

			<div class="mx-auto container">
				<div class="md:grid grid-cols-4 grid-rows-2 gap-6 features-grid">
					<div class="row-span-2">
						<h4>{m.wdm_key_feature_1_title()}</h4>
						<p>{m.wdm_key_feature_1_desc()}</p>
					</div>
					<div class="row-span-2">
						<h4>{m.wdm_key_feature_2_title()}</h4>
						<p>{m.wdm_key_feature_2_desc()}</p>
					</div>
					<div class="row-span-2 bluey">
						<h4>{m.wdm_key_feature_3_title()}</h4>
						<p>{m.wdm_key_feature_3_desc()}</p>
					</div>
					<div class="row-span-2">
						<h4>{m.wdm_key_feature_4_title()}</h4>
						<p>{m.wdm_key_feature_4_desc()}</p>
					</div>
					<div class="row-span-2 col-start-1 row-start-3 bluey">
						<h4>{m.wdm_key_feature_5_title()}</h4>
						<p>{m.wdm_key_feature_5_desc()}</p>
					</div>
					<div class="row-span-2 col-start-2 row-start-3">
						<h4>{m.wdm_key_feature_6_title()}</h4>
						<p>{m.wdm_key_feature_6_desc()}</p>
					</div>
					<div class="row-span-2 col-start-3 row-start-3">
						<h4>{m.wdm_key_feature_7_title()}</h4>
						<p>{m.wdm_key_feature_7_desc()}</p>
					</div>
					<div class="row-span-2 col-start-4 row-start-3 bluey">
						<h4>{m.wdm_key_feature_8_title()}</h4>
						<p>{m.wdm_key_feature_8_desc()}</p>
					</div>
				</div>
			</div>

		</section>

		<section id="tryby-wdm" class="bg-white">

			<div class="md:flex flex-col mx-auto container">
				<div class="mx-auto text-center relative mb-12">
					<h2 class="prehead">{m.wdm_modes_title()}</h2>
					<h3 class="mb-8">{m.wdm_modes_subtitle()}</h3>
				</div>

				<div class="wdm-comparison">
					<!-- Sticky Header -->
					<div class="wdm-sticky-header mb-6 hidden md:flex w-full gap-8 bg-white py-4 px-4 rounded-xl border border-[#BECDF8]">
						<div class="w-full text-center">
							<h4 class="text-blue-300 font-semibold">{m.wdm_mode_regon()}</h4>
						</div>
						<div class="w-full text-center">
							<h4 class="text-blue-300 font-semibold">{m.wdm_mode_phone()}</h4>
						</div>
						<div class="w-full text-center">
							<h4 class="text-blue-300 font-semibold">{m.wdm_mode_list()}</h4>
						</div>
					</div>

					<!-- Features -->
					<div class="space-y-4">
						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_mode_description()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value">{m.wdm_mode_regon_desc()}</div>
								<div class="wdm-feature-value">{m.wdm_mode_phone_desc()}</div>
								<div class="wdm-feature-value">{m.wdm_mode_list_desc()}</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_sharing_between()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">{m.wdm_table_sharing_imaging_ordering()}</div>
								<div class="wdm-feature-value text-center">{m.wdm_table_sharing_medical_single()}</div>
								<div class="wdm-feature-value text-center">{m.wdm_table_sharing_medical_facility()}</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_recipient_determination()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">{m.wdm_table_service()}</div>
								<div class="wdm-feature-value text-center">{m.wdm_table_client()}</div>
								<div class="wdm-feature-value text-center">{m.wdm_table_service()}</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_study_anonymization()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										{m.wdm_table_no()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_dicom_anonymization()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										{m.wdm_table_no()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_email_notifications()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										{m.wdm_table_no()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										{m.wdm_table_no()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_sms_notifications()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										{m.wdm_table_no()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_automatic_transfer()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_commenting()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										{m.wdm_table_no()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✕</span>
										</span>
										{m.wdm_table_no()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_study_description()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_web_viewer()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>

						<div class="wdm-feature-item">
							<div class="wdm-feature-header">
								<h4>{m.wdm_table_dicom_download()}</h4>
							</div>
							<div class="wdm-feature-content">
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
								<div class="wdm-feature-value text-center">
									<span class="inline-flex items-center gap-2">
										<span class="w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
											<span class="text-white text-xs font-bold">✓</span>
										</span>
										{m.wdm_table_yes()}
									</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

		</section>

		<section id="co-mowia" class="bg-blue-900">
			<div class="flex mx-auto container">
				<div class="md:w-1/2 mx-auto text-center relative">
					<h2 class="prehead">{m.wdm_faq_title()}</h2>
					<h3 class="mb-8">{m.wdm_faq_subtitle()}</h3>
					<svelte:component this={WdmAccordion} />
				</div>
			</div>
		</section>

		<WhyChooseSection />

		<FinancingSection />

		<ProcessSection />

		<ContactSection product="wdm" />

		<SeeAlsoSection currentProduct="wdm" />

		<Carousel></Carousel>

	</main>
</div>

<style lang="postcss">
	.features-grid > div {
		@apply bg-white rounded-2xl p-6;
		box-shadow: 0px 12px 24px 0px rgba(125, 153, 232, 0.08);
	}
	.features-grid div.bluey {
		background: linear-gradient(180deg, #0848FF 0%, #0039D6 100%);
		box-shadow: none;
	}
	.features-grid > div.nopadding {
		@apply p-0 overflow-hidden;
	}

	.features-grid > div.nopadding img {
		height: 100%;
		object-fit: cover;
	}
	.features-grid > div h4 {
		@apply text-blue-300 mb-2;
	}
	.features-grid > div h5 {
		@apply text-blue-300;
	}
	.features-grid > div.bluey h4, .features-grid > div.bluey h5, .features-grid > div.bluey p {
		@apply text-white;
	}
	section ul {
		@apply text-blue-300;
	}
	section p {
		@apply text-blue-300 mb-4;
	}
	section h2 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h2.prehead {
		font-size: 1.3125rem;
		@apply text-blue-300;
	}
	section h3 {
		font-size: 2rem;
		@apply text-blue-300;
	}
	section h4 {
		font-size: 1.3125rem;
		@apply text-blue-300 font-normal;
	}
	section {
		@apply py-32;
	}

	/* WDM Comparison Styles */
	.wdm-feature-item {
		@apply bg-white border border-[#BECDF8] rounded-xl overflow-hidden;
	}

	.wdm-feature-header {
		@apply px-6 py-4 text-left border-b border-[#BECDF8];
	}

	.wdm-feature-header h4 {
		@apply text-blue-300 mb-0 text-base;
	}

	.wdm-feature-content {
		@apply grid grid-cols-1 md:grid-cols-3 ;
	}

	.wdm-feature-content div {
		@apply text-blue-100;
	}

	.wdm-feature-value {
		@apply px-6 py-4 text-blue-300 border-b border-[#BECDF8] md:border-r md:border-b-0 last:border-r-0 last:border-b-0;
	}

	/* Sticky Header */
	.wdm-sticky-header {
		position: normal;
		top: 0;
		z-index: 10;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		transition: all 0.3s ease;
	}

	.wdm-sticky-header.stuck {
		box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
	}

	#kluczowe-funkcje p {
		@apply mb-0 pb-0;
	}
	@media screen and (max-width: 1024px) {
		section {
			@apply py-8;
		}
		h3 br, h4 br {
			@apply hidden;
		}
		#kluczowe-funkcje div > div {
			@apply mb-6
		}
		.products > div {
			@apply border p-8 rounded-2xl mb-6;
			border: 1px solid #BECDF8;
		}

		.wdm-feature-content {
			@apply grid-cols-1;
		}

		.wdm-feature-value {
			@apply border-b border-r-0;
		}
	}
</style>
