<script lang="ts">
	import * as m from "$lib/paraglide/messages.js";
	import { languageTag } from "$lib/paraglide/runtime";
	import Banner from "$lib/components/Banner.svelte";
	import Cover from '$lib/assets/images/hero/hero-mf.webp';
	// import MfEcosystem from '$lib/assets/images/mf_ecosystem.svg';
	import MfAccordion from "$lib/components/MfAccordion.svelte";
	import MfEcosystem from "$lib/assets/images/ecosystem.png"
	import { onMount } from 'svelte';
  	import Carousel from "$lib/components/Carousel.svelte";

	// Counter animation
	let countersVisible = false;
	let counters = [
		{ value: 0, target: 220000, suffix: '', label: m.professionals_counters_1() },
		{ value: 0, target: 1300000, suffix: '', label: m.professionals_counters_2() },
		{ value: 0, target: 12000, suffix: '', label: m.professionals_counters_3() }
	];

	function animateCounters() {
		counters.forEach((counter) => {
			const duration = 2000; // 2 seconds
			const steps = 60;
			const increment = counter.target / steps;
			const stepTime = duration / steps;

			let currentStep = 0;
			const timer = setInterval(() => {
				if (currentStep < steps) {
					counter.value = Math.min(counter.value + increment, counter.target);
					counters = [...counters]; // Trigger reactivity
					currentStep++;
				} else {
					counter.value = counter.target;
					counters = [...counters];
					clearInterval(timer);
				}
			}, stepTime);
		});
	}

	onMount(() => {
		const observer = new IntersectionObserver((entries) => {
			entries.forEach((entry) => {
				if (entry.isIntersecting && !countersVisible) {
					countersVisible = true;
					animateCounters();
				}
			});
		});

		const countersSection = document.getElementById('counters');
		if (countersSection) {
			observer.observe(countersSection);
		}

		return () => observer.disconnect();
	});
</script>

<svelte:head>
	<title>{m.professionals_page_title()}</title>
	<meta name="description" content={m.professionals_page_description()} />
</svelte:head>


<Banner src={Cover} >
	<div slot="bannerContent" class="text-blue-300">
		<span class="inline-block px-4 py-2 mb-4 bg-blue-900 rounded-lg text-blue-500 font-normal">{m.need_more()}</span>
		<h1 class="text-3xl mb-4 font-medium">{m.service_level_upgrade()}</h1>
		<p class="text-blue-100">{m.complex_solutions()}</p>
		<a href="#products" class="btn mt-4 mr-4">{m.products_know()}</a>
	</div>
</Banner>

<!-- Sekcja z opisem produktów -->
<section class="bg-white py-32">
	<div class="container mx-auto">
		<div class="md:flex items-center gap-16">
			<div class="md:w-1/2 mb-8 md:mb-0">
				<div class="bg-gray-200 rounded-2xl h-96 flex items-center justify-center">
					<img src="{MfEcosystem}" class="p-4 md:p-16" alt="">
				</div>
			</div>
			<div class="md:w-1/2">
				<h2 class="text-3xl font-medium text-blue-300 mb-6">{m.professionals_section_title()}</h2>
				<p class="text-blue-100 mb-4">
					{m.professionals_section_p1()}
				</p>
				<p class="text-blue-100 mb-4">
					{m.professionals_section_p2()}
				</p>
				<p class="text-blue-100 mb-6">
					{m.professionals_section_p3()}
				</p>
				<a href="#products" class="btn">{m.professionals_section_btn()}</a>
			</div>
		</div>
	</div>
</section>

<!-- Sekcja z licznikami -->
<section id="counters" class="bg-blue-900 py-32">
	<div class="container mx-auto">
		<div class="grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
			{#each counters as counter}
				<div class="text-white">
					<div class="text-4xl md:text-5xl font-bold text-blue-500 mb-2">
						{Math.floor(counter.value).toLocaleString('pl-PL')}{counter.suffix}
					</div>
					<p class="text-blue-100 text-lg">{counter.label}</p>
				</div>
			{/each}
		</div>
	</div>
</section>

<section class="bg-white py-32" id="products">
	<div class="container mx-auto">
		<svelte:component this={MfAccordion} />
	</div>
</section>


<Carousel></Carousel>

<style lang="postcss">

</style>