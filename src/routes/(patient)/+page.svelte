<script lang="ts">
  import * as m from "$lib/paraglide/messages.js";
  import { languageTag } from "$lib/paraglide/runtime";
  import Banner from "$lib/components/Banner.svelte";
  import { animate, inView, stagger } from "motion";
  import PpMockup from "$lib/assets/images/pp_mockup_2.jpg";
  import Carousel from "$lib/components/Carousel.svelte";

  onMount(async () => {
    if (document.querySelector("#gridCardSection")) {
      inView(".gridCard", ({ target }) => {
        animate(
          ".gridCard",
          { transform: "translateY(0px)" },
          { delay: stagger(0.25), duration: 1, easing: [0.17, 0.55, 0.55, 1] },
        );
      });
    }
  });

  import icon1 from "$lib/assets/images/icons/patient-1.svg";
  import icon2 from "$lib/assets/images/icons/patient-2.svg";
  import icon3 from "$lib/assets/images/icons/patient-3.svg";
  import icon4 from "$lib/assets/images/icons/patient-4.svg";
  import DoctorImg from "$lib/assets/images/doctor.jpg";
  import Cover from "$lib/assets/images/hero/hero-patient.webp";

  import EnContent from "./en.svelte";
  import PlContent from "./pl.svelte";

  $: lang = languageTag();

  import PostCard from "$lib/components/PostCard.svelte";
  import { onMount } from "svelte";
  export let data;
</script>

<svelte:head>
  <title>Zbadani.pl | {m.forPatient()}</title>
  <meta name="description" content="Zbadani.pl - sprawdź wyniki badań online, umów wizytę i zarządzaj swoim zdrowiem. Bezpieczny dostęp do dokumentacji medycznej 24/7." />
</svelte:head>

<Banner src={Cover}>
  <div slot="bannerContent" class="text-blue-300">
    <span
      class="inline-block px-4 py-2 mb-4 bg-blue-900 rounded-lg text-blue-500 font-normal"
      >{m.patientportal()}</span
    >
    <h1 class="text-3xl mb-4 font-medium">
      {m.twoje_zdrowie_jest_najwazniejsze()}
    </h1>
    <p class="text-blue-100">
      {m.patient_banner_2()}
    </p>
    <a
    href="https://pacjent.zbadani.pl/register"
    target="_blank"
    class="btn mt-4 md:hidden w-full text-center">{m.login()}</a
  >
    <a
      href="https://pacjent.zbadani.pl/register"
      target="_blank"
      class="btn mt-4 w-full lg:w-auto text-center">{m.patient_banner_2_zaloz_konto()}</a
    >
    <a
      href="https://pacjent.zbadani.pl/register"
      target="_blank"
      class="btn mt-4 md:hidden w-full text-center">{m.results()}</a
    >
  </div>
</Banner>

<section id="gridCardSection" class="mt-24 mb-12">
  <h2 class="font-medium text-center text-3xl text-blue-300 mb-12 px-4">
    {m.patient_grid_h2()}
  </h2>

  <div class="grid gap-12 lg:grid-cols-2 mb-20 mx-auto container items-center">
    <div>
      <img src={PpMockup} alt="Dla Pacjenta" />
    </div>
    <div class="lg:relative lg:-top-8">
      <h3 class="text-lg font-medium text-blue-500">
        {m.patient_grid_1_h3()}
      </h3>
      <p>
        {m.patient_grid_1_p()}
      </p>
      <div class="mb-8"></div>
      <h3 class="text-lg font-medium text-blue-500">{m.patient_grid_2_h3()}</h3>
      <p>
        {m.patient_grid_2_p()}
      </p>
      <div class="mb-8"></div>
      <h3 class="text-lg font-medium text-blue-500">
        {m.patient_grid_3_h3()}
      </h3>
      <p>
        {m.patient_grid_3_p()}
      </p>
    </div>
  </div>

  <div
    class="grid lg:gap-12 gap-4 grid-cols-1 md:grid-cols-3 w-full container mx-auto text-sm text-blue-300"
  >
    <div class="gridCard">
      <span class="inline-block w-16 h-16 rounded-full bg-[#EAF2FD] p-4"
        ><img width="32" height="32" src={icon1} alt="" /></span
      >
      <p class="text-lg leading-6 mt-4">
        {m.patient_benefit_1()}
      </p>
    </div>
    <div class="gridCard">
      <span class="inline-block w-16 h-16 rounded-full bg-[#F3EFFA] p-4"
        ><img width="32" height="32" src={icon2} alt="" /></span
      >
      <p class="text-lg leading-6 mt-4">
        {m.patient_benefit_2()}
      </p>
    </div>
    <div class="gridCard">
      <span class="inline-block w-16 h-16 rounded-full bg-[#FFEFE8] p-4"
        ><img width="32" height="32" src={icon4} alt="" /></span
      >
      <p class="text-lg leading-6 mt-4">
        <span class="uppercase text-blue-500 text-xs">{m.soon()}</span><br />{m.patient_benefit_3()}
      </p>
    </div>
  </div>
  <div class="mx-auto text-center container mt-12">
    <a href="https://pacjent.zbadani.pl/register" target="_blank" class="btn"
      >{m.createAccount()}</a
    >
  </div>
</section>

{#if lang == "pl"}
  <section class="my-12 py-24 bg-blue-900">
    <h1 class="text-2xl font-medium text-blue-300 text-center">
      {m.patient_health_guide()}
    </h1>
    <p class="text-blue-100 text-center">
      {m.patient_health_guide_p()}
    </p>
    <div
      class="container mx-auto grid grid-cols-1 md:grid-cols-3 gap-12 w-full pt-12"
    >
      <PostCard posts={data.pages} />
    </div>
  </section>
{/if}

<section class="lg:mt-12 lg:mb-24 mb-12">
  <div class="container mx-auto max-w-4xl">
    <div class="md:flex content-center items-center">
      <div class="shrink-0 lg:w-80 w-full">
        <img src={DoctorImg} class="rounded-2xl" alt="Dla profesjonalistów" />
      </div>
      <div class="lg:p-16 lg:mt-0 mt-8">
        <span class="inline-block text-blue-500 uppercase">
          {m.pro_are_you()}
        </span>
        <h2 class="font-medium text-3xl mb-4 text-blue-300">
          {m.pro_h2()}
        </h2>
        <p class="mb-4">
          {m.pro_p()}
        </p>
        <a
          href="/professionals"
          class="lg:mt-0 mt-4 lg:w-auto w-full text-center btn"
          >{m.services_for_mf()}</a
        >
      </div>
    </div>
  </div>
</section>

<Carousel></Carousel>

{#if lang == "pl"}
  <svelte:component this={PlContent} />
{/if}
{#if lang == "en"}
  <svelte:component this={EnContent} />
{/if}

<style lang="postcss">
  .gridCard {
    @apply translate-y-12 p-6 border-2 border-blue-900 rounded-2xl flex flex-col;
  }
  .gridCard p {
    @apply mb-4;
  }
</style>
